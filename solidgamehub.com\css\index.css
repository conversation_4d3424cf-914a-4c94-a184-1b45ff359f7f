/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* Header styles are now in common.css */

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
    text-decoration: none;
    font-size: 20px;
    font-weight: bold;
}

.logo img {
    width:200px;
    height: 50px;
  
}

.nav-menu {
    display: flex;
    gap: 25px;
    list-style: none;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 20px;
}


/* User Points Display */
.user-points {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 20px;
    color: white;
    font-weight: bold;
}

.points-icon {
    font-size: 18px;
}

/* Main Content */
.main-content {
    padding: 40px 20px;
}

.page-header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.page-header h1 {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 12px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-header p {
    font-size: 18px;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Quiz Container */
.quiz-container {
    max-width: 600px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 25px;
    padding-bottom: 40px;
}

/* Quiz Cards */
.quiz-card {
    background: white;
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    background-clip: padding-box;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.quiz-card:hover {
    transform: translateY(-5px);
}

.quiz-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
    background-size: 300% 300%;
    animation: gradientShift 3s ease infinite;
    z-index: -1;
    margin: -2px;
    border-radius: 20px;
}

@keyframes gradientShift {

    0%,
    100% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }
}

.quiz-content {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.quiz-image {
    width: 90px;
    height: 90px;
    border-radius: 16px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36px;
    flex-shrink: 0;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.quiz-info {
    flex: 1;
}

.quiz-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #2c3e50;
}

.quiz-description {
    font-size: 14px;
    color: #7f8c8d;
    margin-bottom: 15px;
    line-height: 1.5;
}

.quiz-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 14px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #2ecc71;
}

.quiz-details {
    display: flex;
    flex-direction: column;
    gap: 6px;
    font-size: 13px;
    color: #7f8c8d;
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.countdown {
    font-weight: bold;
    color: #e74c3c;
    font-family: 'Courier New', monospace;
}

.play-button {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 25px;
    font-size: 15px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
    width: 100%;
}

.play-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 157, 0.6);
}

.play-button:active {
    transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
    #userPoints,
    nav {
        display: none;
    }

    .quiz-container {
        max-width: 100%;
        gap: 20px;
        padding: 0 15px 40px;
    }

    .quiz-content {
        gap: 15px;
    }

    .quiz-image {
        width: 70px;
        height: 70px;
        font-size: 28px;
    }

    .page-header h1 {
        font-size: 28px;
    }

    .page-header p {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 20px 10px;
    }

    .quiz-container {
        padding: 0 10px 40px;
    }
}
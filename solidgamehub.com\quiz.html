<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz Challenge -Solidgamehub.com</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/quiz.css">
    <style>
        /* Mobile responsive styles */
        @media (max-width: 768px) {
            #userPoints,
            nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="site-header">
        <div class="header-content">
            <a href="index.html" class="logo">
                <img src="images/logo.png" alt="Solidgamehub.com">
              
            </a>
            
            <div class="user-points" id="userPoints">
                <span class="points-icon">💎</span>
                <span id="pointsValue">0</span>
            </div>
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                ☰
            </button>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>
    <div class="mobile-menu" id="mobileMenu">
        <button class="mobile-menu-close" id="mobileMenuClose">✕</button>
        <ul class="mobile-nav-menu">
            <li><a href="index.html">🏠 Home</a></li>
            <li><a href="about.html">ℹ️ About</a></li>
            <li><a href="privacy.html">🔒 Privacy Policy</a></li>
            <li><a href="terms.html">📋 Terms of Service</a></li>
            <li>
                <div style="padding: 15px 20px; border-top: 1px solid #eee; margin-top: 20px;">
                    <div style="display: flex; align-items: center; gap: 10px; color: #667eea; font-weight: bold;">
                        <span>💎</span>
                        <span id="mobilePointsValue">0</span>
                        <span>Points</span>
                    </div>
                </div>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="quiz-container">
        <div class="quiz-header">
            <div class="quiz-icon" id="quizIcon">🎯</div>
            <div class="quiz-title" id="quizTitle">Quiz Challenge</div>
            <div class="quiz-description" id="quizDescription">Get ready to start the challenge!</div>
        </div>

        <div class="quiz-info" id="quizInfo" style="display: none;">
            <div class="info-item">
                <span class="info-label">Status:</span>
                <span class="info-value">🟢 Live</span>
            </div>
            <div class="info-item">
                <span class="info-label">Entry Fee:</span>
                <span class="info-value">💰 Free</span>
            </div>
            <div class="info-item">
                <span class="info-label">Prize Pool:</span>
                <span class="info-value" id="prizeMoney">🪙 0</span>
            </div>
        </div>

        <!-- Loading State -->
        <div class="loading" id="loadingState">
            <h2>🔄 Loading...</h2>
            <p>Preparing your quiz questions, please wait...</p>
        </div>

        <!-- Quiz Interface -->
        <div id="quizInterface" style="display: none;">
            <div class="quiz-progress">
                <span id="questionNumber">1/10</span>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 10%"></div>
                </div>
                <span id="timeRemaining">⏱️ 15s</span>
            </div>

            <div class="question-container">
                <div class="question-text" id="questionText">
                    Loading question...
                </div>
                <div class="options-container" id="optionsContainer">
                    <!-- Options will be dynamically generated by JavaScript -->
                </div>
            </div>

            <div class="quiz-actions">
                <button class="action-button next-button" id="nextButton" onclick="nextQuestion()" disabled>
                    Next Question
                </button>
            </div>
        </div>

        <!-- Results Page -->
        <div class="results-container" id="resultsContainer" style="display: none;">
            <div class="score-display" id="finalScore">0/10</div>
            <div class="score-text" id="scoreText">Congratulations on completing the challenge!</div>
            <div class="quiz-actions">
                <button class="action-button back-button" onclick="restartQuiz()">
                    🔄 Try Again
                </button>
                <a href="index.html" class="action-button back-button" style="text-decoration: none; display: flex; align-items: center; justify-content: center;">
                    🏠 Back to Home
                </a>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="about.html">About Us</a>
                <a href="privacy.html">Privacy Policy</a>
                <a href="terms.html">Terms of Service</a>
               
            </div>
            <p class="footer-text">© 2025 solidgamehub.com All rights reserved. Challenge yourself, learn something new!</p>
        </div>
    </footer>

    <script src="js/common.js"></script>
    <script src="questions.js"></script>
    <script>
        // Global variables
        let currentQuiz = null;
        let questions = [];
        let currentQuestionIndex = 0;
        let selectedAnswer = null;
        let score = 0;
        let timeLeft = 15;
        let timer = null;
        let isAnswered = false;
        let pointsEarned = 0;

        // Points System
        function getUserPoints() {
            return parseInt(localStorage.getItem('userPoints') || '0');
        }

        function setUserPoints(points) {
            localStorage.setItem('userPoints', points.toString());
            updatePointsDisplay();
        }

        function addPoints(points) {
            const currentPoints = getUserPoints();
            setUserPoints(currentPoints + points);
            pointsEarned += points;
        }

        // Quiz-specific functions (common functions are in common.js)

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // Level and achievement system
        function getUserLevel() {
            const points = getUserPoints();
            if (points >= 50000) return { level: 'Grandmaster', icon: '👑', color: '#FFD700' };
            if (points >= 25000) return { level: 'Expert', icon: '🏆', color: '#C0C0C0' };
            if (points >= 15000) return { level: 'Advanced', icon: '🥇', color: '#CD7F32' };
            if (points >= 8000) return { level: 'Intermediate', icon: '🥈', color: '#4CAF50' };
            if (points >= 3000) return { level: 'Beginner', icon: '🥉', color: '#2196F3' };
            return { level: 'Novice', icon: '🌟', color: '#9C27B0' };
        }

        function getPerformanceRating(score, totalQuestions, pointsEarned) {
            const percentage = (score / totalQuestions) * 100;
            const avgPointsPerQuestion = pointsEarned / totalQuestions;

            if (percentage === 100 && avgPointsPerQuestion >= 130) {
                return {
                    rating: 'Perfect Master',
                    icon: '👑',
                    color: '#FFD700',
                    message: 'Absolutely flawless! You are a true quiz master!',
                    bonus: 500
                };
            } else if (percentage >= 90 && avgPointsPerQuestion >= 120) {
                return {
                    rating: 'Excellent',
                    icon: '🏆',
                    color: '#FF6B35',
                    message: 'Outstanding performance! You\'re among the elite!',
                    bonus: 300
                };
            } else if (percentage >= 80 && avgPointsPerQuestion >= 110) {
                return {
                    rating: 'Great',
                    icon: '🥇',
                    color: '#4CAF50',
                    message: 'Great job! Your knowledge is impressive!',
                    bonus: 200
                };
            } else if (percentage >= 70) {
                return {
                    rating: 'Good',
                    icon: '🥈',
                    color: '#2196F3',
                    message: 'Good work! Keep practicing to improve!',
                    bonus: 100
                };
            } else if (percentage >= 50) {
                return {
                    rating: 'Fair',
                    icon: '🥉',
                    color: '#FF9800',
                    message: 'Not bad! There\'s room for improvement!',
                    bonus: 50
                };
            } else {
                return {
                    rating: 'Needs Practice',
                    icon: '📚',
                    color: '#9C27B0',
                    message: 'Keep studying! Practice makes perfect!',
                    bonus: 25
                };
            }
        }

        // Get URL parameter
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // Initialize page
        async function initializePage() {
            const quizId = getUrlParameter('quiz');

            if (!quizId) {
                window.location.href = 'index.html';
                return;
            }

            // Wait for data loading
            await loadQuestionBank();

            currentQuiz = getQuizInfo(quizId);
            if (!currentQuiz) {
                alert('Quiz not found!');
                window.location.href = 'index.html';
                return;
            }

            // Set page information
            document.getElementById('quizIcon').textContent = currentQuiz.icon;
            document.getElementById('quizTitle').textContent = currentQuiz.title;
            document.getElementById('quizDescription').textContent = currentQuiz.description;
            document.getElementById('prizeMoney').textContent = `🏆 ${currentQuiz.prize} pts`;
            document.title = `${currentQuiz.title} -Solidgamehub.com`;

            // Get questions
            questions = getRandomQuestions(quizId, 10);

            if (questions.length === 0) {
                alert('No questions available for this quiz!');
                window.location.href = 'index.html';
                return;
            }

            // Initialize quiz-specific data
            pointsEarned = 0;

            // Start quiz
            setTimeout(startQuiz, 1500);
        }

        // Start quiz
        function startQuiz() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('quizInterface').style.display = 'block';

            currentQuestionIndex = 0;
            score = 0;
            showQuestion();
        }

        // Show question
        function showQuestion() {
            if (currentQuestionIndex >= questions.length) {
                showResults();
                return;
            }

            const question = questions[currentQuestionIndex];
            isAnswered = false;
            selectedAnswer = null;
            timeLeft = 15;

            // Update progress
            document.getElementById('questionNumber').textContent = `${currentQuestionIndex + 1}/${questions.length}`;
            const progress = ((currentQuestionIndex + 1) / questions.length) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;

            // Show question
            document.getElementById('questionText').textContent = question.question;

            // Generate options
            const optionsContainer = document.getElementById('optionsContainer');
            optionsContainer.innerHTML = '';

            question.options.forEach((option, index) => {
                const button = document.createElement('button');
                button.className = 'option-button';
                button.textContent = option;
                button.onclick = () => selectAnswer(index);
                optionsContainer.appendChild(button);
            });

            // Reset button state
            document.getElementById('nextButton').disabled = true;
            const nextBtn = document.getElementById('nextButton');
            nextBtn.textContent = currentQuestionIndex === questions.length - 1 ? 'View Results' : 'Next Question';

            // Start timer
            startTimer();
        }

        // Start timer
        function startTimer() {
            if (timer) clearInterval(timer);

            timer = setInterval(() => {
                if (!isAnswered) {  // Only decrease time if not answered
                    timeLeft--;
                    document.getElementById('timeRemaining').textContent = `⏱️ ${timeLeft}s`;

                    if (timeLeft <= 0) {
                        clearInterval(timer);
                        // Time's up, auto-select wrong answer
                        selectAnswer(-1);
                    }
                }
            }, 1000);
        }

        // Select answer
        function selectAnswer(answerIndex) {
            if (isAnswered) return;

            isAnswered = true;
            selectedAnswer = answerIndex;
            clearInterval(timer); // Stop timer when answer is selected

            const question = questions[currentQuestionIndex];
            const options = document.querySelectorAll('.option-button');

            // Show correct answer
            options.forEach((option, index) => {
                option.onclick = null; // Disable clicking

                if (index === question.correct) {
                    option.classList.add('correct');
                } else if (index === selectedAnswer && index !== question.correct) {
                    option.classList.add('incorrect');
                } else if (index === selectedAnswer) {
                    option.classList.add('selected');
                }
            });

            // Check answer and award points
            if (selectedAnswer === question.correct) {
                score++;
                // Award points based on time remaining (bonus for quick answers)
                const timeBonus = Math.max(1, Math.floor(timeLeft / 3)); // Adjusted for 15-second timer
                const pointsForQuestion = 100 + (timeBonus * 15);
                addPoints(pointsForQuestion);
            }

            // Enable next button
            document.getElementById('nextButton').disabled = false;
        }

        // Next question
        function nextQuestion() {
            currentQuestionIndex++;
            showQuestion();
        }

        // Show results
        function showResults() {
            document.getElementById('quizInterface').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';

            const percentage = Math.round((score / questions.length) * 100);
            document.getElementById('finalScore').textContent = `${score}/${questions.length}`;

            // Get performance rating
            const avgPointsPerQuestion = pointsEarned / questions.length;
            const performance = getPerformanceRating(score, questions.length, pointsEarned);

            // Award performance bonus
            if (performance.bonus > 0) {
                addPoints(performance.bonus);
                pointsEarned += performance.bonus;
            }

            // Get current user level
            const userLevel = getUserLevel();

            // Create enhanced results display
            const scoreTextElement = document.getElementById('scoreText');
            scoreTextElement.innerHTML = `
                <div style="margin-bottom: 20px;">
                    <div style="font-size: 24px; color: ${performance.color}; margin-bottom: 10px;">
                        ${performance.icon} ${performance.rating}
                    </div>
                    <div style="font-size: 18px; margin-bottom: 15px;">
                        ${performance.message}
                    </div>
                    <div style="font-size: 16px; color: #666; margin-bottom: 15px;">
                        Accuracy: ${percentage}% | Avg Speed Bonus: ${Math.round(avgPointsPerQuestion - 100)}pts
                    </div>
                </div>

                <div style="background: linear-gradient(135deg, ${userLevel.color}20, ${userLevel.color}10);
                           border: 2px solid ${userLevel.color}; border-radius: 15px; padding: 20px; margin: 20px 0;">
                    <div style="text-align: center;">
                        <div style="font-size: 20px; margin-bottom: 10px;">
                            ${userLevel.icon} Current Level: ${userLevel.level}
                        </div>
                        <div style="font-size: 16px; color: #666;">
                            Total Points: ${getUserPoints().toLocaleString()}
                        </div>
                    </div>
                </div>
            `;

            // Show points earned
            if (pointsEarned > 0) {
                const pointsDisplay = document.createElement('div');
                pointsDisplay.className = 'points-earned';
                pointsDisplay.innerHTML = `
                    <div style="font-size: 18px; font-weight: bold; margin-bottom: 5px;">
                        💎 +${pointsEarned} Points Earned!
                    </div>
                    ${performance.bonus > 0 ? `<div style="font-size: 14px; color: #4CAF50;">Includes ${performance.bonus} performance bonus!</div>` : ''}
                `;
                scoreTextElement.appendChild(pointsDisplay);

                showNotification(`Amazing! You earned ${pointsEarned} points and achieved ${performance.rating} rating!`, 'success');
            }
        }

        // Restart quiz
        function restartQuiz() {
            currentQuestionIndex = 0;
            score = 0;
            selectedAnswer = null;
            isAnswered = false;
            pointsEarned = 0;

            // Get new random questions
            const quizId = getUrlParameter('quiz');
            questions = getRandomQuestions(quizId, 10);

            document.getElementById('resultsContainer').style.display = 'none';
            document.getElementById('quizInterface').style.display = 'block';

            showQuestion();
        }

        // Initialize page when loaded
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>

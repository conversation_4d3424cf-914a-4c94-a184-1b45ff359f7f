// Load question bank data from JSON file
let fullQuestionBank = [];

// Quiz configuration data - based on complete JSON data structure (22 categories)
const quizData = [
    {
        id: 'general-knowledge',
        title: 'General Knowledge',
        icon: '🧠',
        prize: '4500',
        category: 'General Knowledge',
        description: 'Test your broad knowledge across various topics and general facts about the world.'
    },
    {
        id: 'entertainment-film',
        title: 'Movies & Film',
        icon: '🎬',
        prize: '4200',
        category: 'Entertainment: Film',
        description: 'Test your knowledge of cinema history, famous actors, directors, and iconic films.'
    },
    {
        id: 'science-computers',
        title: 'Computer Science',
        icon: '💻',
        prize: '5200',
        category: 'Science: Computers',
        description: 'Challenge yourself with questions about programming, technology, and computer science.'
    },
    {
        id: 'geography',
        title: 'World Geography',
        icon: '🌍',
        prize: '4000',
        category: 'Geography',
        description: 'Explore the world through questions about countries, capitals, landmarks, and natural wonders.'
    },
    {
        id: 'history',
        title: 'History',
        icon: '📚',
        prize: '4800',
        category: 'History',
        description: 'Journey through time and test your knowledge of historical events, figures, and civilizations.'
    },
    {
        id: 'science-nature',
        title: 'Science & Nature',
        icon: '🔬',
        prize: '4600',
        category: 'Science & Nature',
        description: 'Discover the wonders of science and nature with questions about biology, physics, and chemistry.'
    },
    {
        id: 'entertainment-video-games',
        title: 'Video Games',
        icon: '🎮',
        prize: '4400',
        category: 'Entertainment: Video Games',
        description: 'Test your knowledge of gaming history, popular titles, and gaming culture.'
    },
    {
        id: 'science-mathematics',
        title: 'Mathematics',
        icon: '📊',
        prize: '5500',
        category: 'Science: Mathematics',
        description: 'Challenge your mathematical skills with problems involving numbers, logic, and calculations.'
    },
    {
        id: 'entertainment-anime-manga',
        title: 'Anime & Manga',
        icon: '🎌',
        prize: '4100',
        category: 'Entertainment: Japanese Anime & Manga',
        description: 'Challenge your knowledge of Japanese animation, manga series, and anime culture.'
    },
    {
        id: 'entertainment-music',
        title: 'Music',
        icon: '🎵',
        prize: '3900',
        category: 'Entertainment: Music',
        description: 'Test your knowledge of music history, artists, genres, and musical instruments.'
    },
    {
        id: 'sports',
        title: 'Sports',
        icon: '⚽',
        prize: '4300',
        category: 'Sports',
        description: 'Challenge yourself with questions about various sports, athletes, and sporting events.'
    },
    {
        id: 'entertainment-television',
        title: 'Television',
        icon: '📺',
        prize: '3800',
        category: 'Entertainment: Television',
        description: 'Test your knowledge of TV shows, series, and television history.'
    },
    {
        id: 'art',
        title: 'Art & Culture',
        icon: '🎨',
        prize: '4500',
        category: 'Art',
        description: 'Explore the world of art, artists, and cultural movements throughout history.'
    },
    {
        id: 'animals',
        title: 'Animals',
        icon: '🐾',
        prize: '3700',
        category: 'Animals',
        description: 'Test your knowledge about the animal kingdom, wildlife, and zoology.'
    },
    {
        id: 'vehicles',
        title: 'Vehicles',
        icon: '🚗',
        prize: '4000',
        category: 'Vehicles',
        description: 'Challenge yourself with questions about cars, motorcycles, and transportation.'
    },
    {
        id: 'mythology',
        title: 'Mythology',
        icon: '⚡',
        prize: '4700',
        category: 'Mythology',
        description: 'Explore ancient myths, legends, and mythological figures from around the world.'
    },
    {
        id: 'entertainment-books',
        title: 'Books & Literature',
        icon: '📖',
        prize: '4300',
        category: 'Entertainment: Books',
        description: 'Test your knowledge of classic and modern literature, authors, and literary works.'
    },
    {
        id: 'entertainment-board-games',
        title: 'Board Games',
        icon: '🎲',
        prize: '3600',
        category: 'Entertainment: Board Games',
        description: 'Challenge yourself with questions about board games, rules, and gaming strategies.'
    },
    {
        id: 'celebrities',
        title: 'Celebrities',
        icon: '⭐',
        prize: '3500',
        category: 'Celebrities',
        description: 'Test your knowledge about famous personalities, actors, and public figures.'
    },
    {
        id: 'science-gadgets',
        title: 'Science & Gadgets',
        icon: '📱',
        prize: '4400',
        category: 'Science: Gadgets',
        description: 'Explore the world of technology, gadgets, and scientific innovations.'
    },
    {
        id: 'entertainment-comics',
        title: 'Comics',
        icon: '💥',
        prize: '3800',
        category: 'Entertainment: Comics',
        description: 'Test your knowledge of comic books, superheroes, and graphic novels.'
    },
    {
        id: 'entertainment-cartoons',
        title: 'Cartoons & Animation',
        icon: '🎭',
        prize: '3400',
        category: 'Entertainment: Cartoon & Animations',
        description: 'Challenge yourself with questions about animated shows, cartoons, and animation history.'
    }
];

// Asynchronously load question bank data
async function loadQuestionBank() {
    try {
        const response = await fetch('./data/full_question_bank.json');
        fullQuestionBank = await response.json();
        console.log('Question bank loaded successfully:', fullQuestionBank.length, 'categories');
    } catch (error) {
        console.error('Failed to load question bank:', error);
        // Use empty array if loading fails
        fullQuestionBank = [];
    }
}

// Get questions for specified quiz
function getQuizQuestions(quizId) {
    const quiz = quizData.find(q => q.id === quizId);
    if (!quiz) return [];

    // The new JSON structure has categories as keys
    const categoryQuestions = fullQuestionBank[quiz.category];
    if (!categoryQuestions) return [];

    // Convert data format for quiz page compatibility
    return categoryQuestions.map((q, index) => {
        return {
            id: index + 1,
            question: q.question,
            options: q.all_answers,
            correct: q.correct_answer_index,
            explanation: `The correct answer is: ${q.correct_answer}`
        };
    });
}

// Get quiz information
function getQuizInfo(quizId) {
    return quizData.find(quiz => quiz.id === quizId);
}

// Get random questions for quiz
function getRandomQuestions(quizId, count = 10) {
    const allQuestions = getQuizQuestions(quizId);
    if (allQuestions.length === 0) return [];

    // Randomly select specified number of questions
    const shuffled = [...allQuestions].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, allQuestions.length));
}

// Initialize data when page loads
if (typeof window !== 'undefined') {
    // Auto-load data in browser environment
    loadQuestionBank();
}

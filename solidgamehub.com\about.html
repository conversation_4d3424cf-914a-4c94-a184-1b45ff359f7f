<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us -Solidgamehub</title>
    <link rel="stylesheet" href="css/common.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
            font-feature-settings: "kern" 1;
        }

        .main-content {
            padding: 40px 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
            #userPoints,
            nav {
                display: none;
            }
        }

        .about-hero {
            text-align: center;
            margin-bottom: 50px;
            color: white;
        }

        .about-hero h1 {
            font-size: 42px;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            letter-spacing: -0.02em;
            font-feature-settings: "liga" 1, "kern" 1;
        }

        .about-hero p {
            font-size: 20px;
            opacity: 0.9;
            line-height: 1.6;
            font-weight: 400;
            letter-spacing: 0.01em;
        }
        
        .about-content {
            background: white;
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.2);
            margin-bottom: 30px;
        }
        
        .about-section {
            margin-bottom: 40px;
        }
        
        .about-section:last-child {
            margin-bottom: 0;
        }
        
        .about-section h2 {
            font-size: 28px;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            font-weight: 600;
            letter-spacing: -0.01em;
            font-feature-settings: "liga" 1, "kern" 1;
        }

        .about-section p {
            font-size: 16px;
            line-height: 1.8;
            color: #555;
            margin-bottom: 15px;
            font-weight: 400;
            letter-spacing: 0.005em;
            text-align: justify;
            hyphens: auto;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            letter-spacing: -0.005em;
            font-feature-settings: "liga" 1;
        }

        .feature-description {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
            font-weight: 400;
            letter-spacing: 0.01em;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .stat-item {
            text-align: center;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px 15px;
            border-radius: 16px;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: 700;
            display: block;
            margin-bottom: 5px;
            letter-spacing: -0.02em;
            font-feature-settings: "tnum" 1;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 400;
            letter-spacing: 0.02em;
            text-transform: uppercase;
        }
        
        @media (max-width: 768px) {
            .about-hero h1 {
                font-size: 32px;
                letter-spacing: -0.015em;
            }

            .about-hero p {
                font-size: 18px;
                letter-spacing: 0.005em;
            }

            .about-content {
                padding: 25px;
            }

            .about-section h2 {
                font-size: 24px;
                letter-spacing: -0.005em;
            }

            .about-section p {
                text-align: left;
                hyphens: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="site-header">
        <div class="header-content">
            <a href="index.html" class="logo">
                <img src="images/logo.png" alt="Solidgamehub.com">
              
            </a>
            
            <div class="user-points" id="userPoints">
                <span class="points-icon">💎</span>
                <span id="pointsValue">0</span>
            </div>
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                ☰
            </button>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>
    <div class="mobile-menu" id="mobileMenu">
        <button class="mobile-menu-close" id="mobileMenuClose">✕</button>
        <ul class="mobile-nav-menu">
            <li><a href="index.html">🏠 Home</a></li>
            <li><a href="about.html">ℹ️ About</a></li>
            <li><a href="privacy.html">🔒 Privacy Policy</a></li>
            <li><a href="terms.html">📋 Terms of Service</a></li>
            <li>
                <div style="padding: 15px 20px; border-top: 1px solid #eee; margin-top: 20px;">
                    <div style="display: flex; align-items: center; gap: 10px; color: #667eea; font-weight: bold;">
                        <span>💎</span>
                        <span id="mobilePointsValue">0</span>
                        <span>Points</span>
                    </div>
                </div>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="about-hero">
            <h1>🎯 About Solidgamehub</h1>
            <p>Empowering minds through interactive learning and challenging quizzes across 22 comprehensive knowledge categories</p>
        </div>

        <div class="about-content">
            <div class="about-section">
                <h2>🚀 Our Mission</h2>
                <p>At Solidgamehub, we believe that learning should be engaging, fun, and rewarding. Our mission is to create an interactive platform where knowledge seekers can challenge themselves across a comprehensive array of 22 diverse topics, from entertainment and science to history and technology, while earning points and recognition for their achievements.</p>
                <p>With over 2000 carefully curated questions spanning categories including General Knowledge, Computer Science, Entertainment (Film, Music, TV, Games), Geography, History, Science & Nature, Mathematics, and many specialized areas, we strive to make education accessible and enjoyable for everyone, regardless of their interests or expertise level.</p>
            </div>

            <div class="about-section">
                <h2>✨ Key Features</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <span class="feature-icon">🎮</span>
                        <div class="feature-title">Interactive Quizzes</div>
                        <div class="feature-description">Engaging multiple-choice questions with instant feedback and explanations</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">💎</span>
                        <div class="feature-title">Points System</div>
                        <div class="feature-description">Earn points for correct answers and track your progress over time</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📚</span>
                        <div class="feature-title">Diverse Categories</div>
                        <div class="feature-description">75+ categories including Technology, AI, Crypto, Gaming, Anime, and more</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">⏱️</span>
                        <div class="feature-title">Fast-Paced Challenges</div>
                        <div class="feature-description">15-second time limits for quick thinking and rapid-fire knowledge testing</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📱</span>
                        <div class="feature-title">Mobile Friendly</div>
                        <div class="feature-description">Optimized for all devices - play anywhere, anytime</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🏆</span>
                        <div class="feature-title">Achievement System</div>
                        <div class="feature-description">Unlock achievements and compete with yourself to improve</div>
                    </div>
                </div>
            </div>

            <div class="about-section">
                <h2>📚 All 22 Quiz Categories</h2>
                <p>Explore our comprehensive collection of quiz categories covering every area of knowledge:</p>
                <div class="features-grid">
                    <div class="feature-card">
                        <span class="feature-icon">🧠</span>
                        <div class="feature-title">General Knowledge</div>
                        <div class="feature-description">Broad knowledge across various topics and general facts</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🎬</span>
                        <div class="feature-title">Movies & Film</div>
                        <div class="feature-description">Cinema history, actors, directors, and iconic films</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">💻</span>
                        <div class="feature-title">Computer Science</div>
                        <div class="feature-description">Programming, technology, and computer fundamentals</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🌍</span>
                        <div class="feature-title">Geography</div>
                        <div class="feature-description">Countries, capitals, landmarks, and natural wonders</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📚</span>
                        <div class="feature-title">History</div>
                        <div class="feature-description">Historical events, figures, and civilizations</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🔬</span>
                        <div class="feature-title">Science & Nature</div>
                        <div class="feature-description">Biology, physics, chemistry, and natural phenomena</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🎮</span>
                        <div class="feature-title">Video Games</div>
                        <div class="feature-description">Gaming history, popular titles, and gaming culture</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📊</span>
                        <div class="feature-title">Mathematics</div>
                        <div class="feature-description">Mathematical concepts, problems, and calculations</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🎌</span>
                        <div class="feature-title">Anime & Manga</div>
                        <div class="feature-description">Japanese animation, manga series, and anime culture</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🎵</span>
                        <div class="feature-title">Music</div>
                        <div class="feature-description">Music history, artists, genres, and instruments</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">⚽</span>
                        <div class="feature-title">Sports</div>
                        <div class="feature-description">Various sports, athletes, and sporting events</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📺</span>
                        <div class="feature-title">Television</div>
                        <div class="feature-description">TV shows, series, and television history</div>
                    </div>
                </div>

                <div class="features-grid" style="margin-top: 30px;">
                    <div class="feature-card">
                        <span class="feature-icon">🎨</span>
                        <div class="feature-title">Art & Culture</div>
                        <div class="feature-description">Art history, artists, and cultural movements</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🐾</span>
                        <div class="feature-title">Animals</div>
                        <div class="feature-description">Animal kingdom, wildlife, and zoology</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🚗</span>
                        <div class="feature-title">Vehicles</div>
                        <div class="feature-description">Cars, motorcycles, and transportation</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">⚡</span>
                        <div class="feature-title">Mythology</div>
                        <div class="feature-description">Ancient myths, legends, and mythological figures</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📖</span>
                        <div class="feature-title">Books & Literature</div>
                        <div class="feature-description">Classic and modern literature, authors, and works</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🎲</span>
                        <div class="feature-title">Board Games</div>
                        <div class="feature-description">Board games, rules, and gaming strategies</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">⭐</span>
                        <div class="feature-title">Celebrities</div>
                        <div class="feature-description">Famous personalities, actors, and public figures</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📱</span>
                        <div class="feature-title">Science & Gadgets</div>
                        <div class="feature-description">Technology, gadgets, and scientific innovations</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">💥</span>
                        <div class="feature-title">Comics</div>
                        <div class="feature-description">Comic books, superheroes, and graphic novels</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🎭</span>
                        <div class="feature-title">Cartoons & Animation</div>
                        <div class="feature-description">Animated shows, cartoons, and animation history</div>
                    </div>
                </div>
            </div>

            <div class="about-section">
                <h2>📊 Platform Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number">2000+</span>
                        <span class="stat-label">Questions</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">22</span>
                        <span class="stat-label">Categories</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">22</span>
                        <span class="stat-label">Available Quizzes</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">15s</span>
                        <span class="stat-label">Per Question</span>
                    </div>
                </div>
            </div>

            <div class="about-section">
                <h2>🎯 Why Choose Solidgamehub?</h2>
                <p><strong>Educational Value:</strong> Every question is carefully crafted to provide learning opportunities, not just test memorization.</p>
                <p><strong>User Experience:</strong> Clean, modern interface designed for optimal user engagement and accessibility.</p>
                <p><strong>Progress Tracking:</strong> Built-in points system helps you monitor your improvement and stay motivated.</p>
                <p><strong>Variety:</strong> From movies and TV shows to science and mathematics, there's something for everyone.</p>
                <p><strong>Free to Use:</strong> All quizzes are completely free with no hidden costs or premium barriers.</p>
            </div>

            <div class="about-section">
                <h2>🤝 Get Started Today</h2>
                <p>Ready to challenge yourself? Head back to our <a href="index.html" style="color: #667eea; text-decoration: none; font-weight: bold;">homepage</a> and choose your first quiz. Whether you're a movie buff, science enthusiast, or history lover, we have the perfect challenge waiting for you!</p>
                <p>Remember, every correct answer earns you points, and every quiz completed makes you smarter. Start your learning journey withSolidgamehub.com today!</p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="about.html">About Us</a>
                <a href="privacy.html">Privacy Policy</a>
                <a href="terms.html">Terms of Service</a>
               
            </div>
            <p class="footer-text">© 2025 solidgamehub.com All rights reserved. Challenge yourself, learn something new!</p>
        </div>
    </footer>

    <script src="js/common.js"></script>
</body>
</html>

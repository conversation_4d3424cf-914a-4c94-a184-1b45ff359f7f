// Common JavaScript functions for all pages

// Points System
function getUserPoints() {
    return parseInt(localStorage.getItem('userPoints') || '0');
}

function setUserPoints(points) {
    localStorage.setItem('userPoints', points.toString());
    updatePointsDisplay();
}

function addPoints(points) {
    const currentPoints = getUserPoints();
    setUserPoints(currentPoints + points);
    showNotification(`+${points} points earned!`, 'success');
}

function updatePointsDisplay() {
    const points = getUserPoints().toLocaleString();
    const pointsElement = document.getElementById('pointsValue');
    const mobilePointsElement = document.getElementById('mobilePointsValue');
    
    if (pointsElement) {
        pointsElement.textContent = points;
    }
    if (mobilePointsElement) {
        mobilePointsElement.textContent = points;
    }
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => notification.classList.add('show'), 100);
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Mobile Menu Functionality
function initMobileMenu() {
    const menuToggle = document.getElementById('mobileMenuToggle');
    const menuClose = document.getElementById('mobileMenuClose');
    const mobileMenu = document.getElementById('mobileMenu');
    const menuOverlay = document.getElementById('mobileMenuOverlay');

    function openMenu() {
        if (mobileMenu && menuOverlay) {
            mobileMenu.classList.add('active');
            menuOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    function closeMenu() {
        if (mobileMenu && menuOverlay) {
            mobileMenu.classList.remove('active');
            menuOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    if (menuToggle) {
        menuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            openMenu();
        });
    }

    if (menuClose) {
        menuClose.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeMenu();
        });
    }

    if (menuOverlay) {
        menuOverlay.addEventListener('click', function(e) {
            e.preventDefault();
            closeMenu();
        });
    }

    // Close menu when clicking on menu links
    const mobileMenuLinks = document.querySelectorAll('.mobile-nav-menu a');
    mobileMenuLinks.forEach(link => {
        link.addEventListener('click', function() {
            closeMenu();
        });
    });

    // Close menu on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeMenu();
        }
    });
}

// Header Scroll Effect
function initHeaderScrollEffect() {
    const header = document.querySelector('.site-header');
    if (!header) return;

    let isScrolled = false;

    function handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const shouldBeScrolled = scrollTop > 100;

        if (shouldBeScrolled !== isScrolled) {
            isScrolled = shouldBeScrolled;
            if (isScrolled) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        }
    }

    // Use throttling to improve performance
    let ticking = false;
    function throttledHandleScroll() {
        if (!ticking) {
            requestAnimationFrame(() => {
                handleScroll();
                ticking = false;
            });
            ticking = true;
        }
    }

    window.addEventListener('scroll', throttledHandleScroll, { passive: true });

    // Check initial scroll position
    handleScroll();
}

// Back to Top Button
function initBackToTop() {
    // Create back to top button
    const backToTopBtn = document.createElement('button');
    backToTopBtn.className = 'back-to-top';
    backToTopBtn.innerHTML = '↑';
    backToTopBtn.setAttribute('aria-label', 'Back to top');
    backToTopBtn.setAttribute('title', 'Back to top');
    document.body.appendChild(backToTopBtn);

    let isVisible = false;

    function toggleBackToTop() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const shouldShow = scrollTop > 300;

        if (shouldShow !== isVisible) {
            isVisible = shouldShow;
            if (isVisible) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        }
    }

    function scrollToTop() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        if (scrollTop > 0) {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    }

    // Use throttling to improve performance
    let ticking = false;
    function throttledToggleBackToTop() {
        if (!ticking) {
            requestAnimationFrame(() => {
                toggleBackToTop();
                ticking = false;
            });
            ticking = true;
        }
    }

    window.addEventListener('scroll', throttledToggleBackToTop, { passive: true });
    backToTopBtn.addEventListener('click', scrollToTop);

    // Check initial scroll position
    toggleBackToTop();
}

// Initialize common functionality
function initCommon() {
    updatePointsDisplay();
    initMobileMenu();
    initHeaderScrollEffect();
    initBackToTop();
}

// Auto-initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initCommon);
} else {
    initCommon();
}
